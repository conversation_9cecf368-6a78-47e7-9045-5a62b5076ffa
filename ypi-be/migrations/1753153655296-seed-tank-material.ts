import { MigrationInterface, QueryRunner } from 'typeorm'

export class SeedTankMaterial1753153655296 implements MigrationInterface {
  name = 'SeedTankMaterial1753153655296'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      INSERT INTO "tank_material" ("name")
      SELECT * FROM (VALUES
        ('FRP/GRP'),
        ('RC'),
        ('SS')
      ) AS v(name)
      WHERE NOT EXISTS (
        SELECT 1 FROM "tank_material" WHERE "name" = v.name
      );
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM "tank_material"
      WHERE "name" IN ('FRP/GRP', 'RC', 'SS');
    `)
  }
}
