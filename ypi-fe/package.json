{"name": "ypi", "version": "0.1.0", "private": true, "scripts": {"prepare": "panda codegen", "dev": "next dev", "build": "next build --no-lint && cp -r public .next/standalone", "start": "next start", "lint": "next lint"}, "dependencies": {"@cyntler/react-doc-viewer": "^1.16.6", "@floating-ui/react": "^0.26.10", "@headlessui/react": "^1.7.17", "@hookform/resolvers": "^3.3.3", "@react-google-maps/api": "^2.19.3", "@types/react-image-gallery": "^1.2.4", "@types/react-tagsinput": "^3.20.6", "axios": "^1.6.4", "dayjs": "^1.11.10", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "next": "14.0.3", "puppeteer": "^23.1.0", "query-string": "^8.1.0", "react": "^18", "react-animate-height": "^3.2.3", "react-big-calendar": "^1.11.2", "react-datepicker": "^6.4.0", "react-dom": "^18", "react-hook-form": "^7.49.2", "react-hot-toast": "^2.4.1", "react-image-gallery": "^1.3.0", "react-multi-select-component": "^4.3.4", "react-select": "^5.8.0", "react-tagsinput": "^3.20.3", "react-tiny-popover": "^8.0.4", "react-transition-group": "^4.4.5", "swr": "^2.2.4", "zod": "^3.22.4"}, "devDependencies": {"@pandacss/dev": "^0.20.1", "@types/axios": "^0.14.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.202", "@types/node": "^20", "@types/react": "^18", "@types/react-big-calendar": "^1.8.9", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^18", "@types/react-transition-group": "^4.4.10", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "eslint": "8.56.0", "eslint-config-next": "14.0.4", "next-auth": "^4.24.5", "typescript": "^5"}}