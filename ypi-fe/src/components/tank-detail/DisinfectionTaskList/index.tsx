import Table, {IColProps} from '@/components/globals/Table';
import {box, flex} from '@/styled-system/patterns';

import {CATEGORY_ASSIGN_TASK_LABELS} from '@/constant/task';
import ChevronRight from '@/components/globals/Icons/ChevronRight';
import {EAssignTaskStatus} from '@/utilities/types/enums/Task';
import {FORMAT} from '@/configs/date';
import {IJob} from '@/utilities/types/entities/job';
import Link from 'next/link';
import OrderedList from '@/components/globals/OrderedList';
import {Slug} from '@/utilities/types/enums/Slug';
import Typography from '@/components/globals/Typography';
import dayjs from 'dayjs';
import {usePathname, useRouter} from 'next/navigation';

interface Props {
    data: IJob[];
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
    loading?: boolean;
}

export default function DisinfectionTaskList(props: Props) {
    const router = useRouter();
    const pathName = usePathname();
    const columns: IColProps<IJob>[] = [
        {
            dataKey: 'id',
            title: 'Job ID',
            key: 'id',
            align: 'left',
            valign: 'top',
            width: 130,
            render: (row: IJob) => {
                return (
                    <div
                        className={flex({
                            direction: 'column',
                            gap: 1,
                        })}
                    >
                        <Typography
                            typography="header_15"
                            color="secondary_100"
                        >
                            {row?.startedAt && dayjs(row?.startedAt).isValid()
                                ? dayjs(row?.startedAt).format(
                                      FORMAT.DATE_PICKER
                                  )
                                : ''}
                        </Typography>
                        <Typography
                            color="secondary_100"
                            typography="body_12"
                            className={box({
                                textTransform: 'uppercase',
                            })}
                        >
                            {row.code}
                        </Typography>
                    </div>
                );
            },
        },
        {
            dataKey: 'status',
            title: 'Status',
            key: 'status',
            align: 'left',
            valign: 'top',
            width: 150,
            render: (row) => {
                return (
                    <Typography
                        color={
                            CATEGORY_ASSIGN_TASK_LABELS?.[
                                row?.status as EAssignTaskStatus
                            ]?.color as any
                        }
                        typography="header_15"
                    >
                        {
                            CATEGORY_ASSIGN_TASK_LABELS?.[
                                row?.status as EAssignTaskStatus
                            ]?.label
                        }
                    </Typography>
                );
            },
        },
        {
            dataKey: 'defects',
            title: 'Defects',
            key: 'defects',
            align: 'left',
            valign: 'top',
            render: (row) => {
              return (
                <OrderedList
                  defects={row?.defects || []}
                  noDefects = {row?.noDefects || []}
                  completed = {row?.completed || []}
                  unCompleted = {row?.unCompleted || []}
                  overflow="ellipsis"
                />
              )
            },
        },
        {
            dataKey: '',
            title: '',
            key: 'actions',
            align: 'right',
            width: 64,
            render: (row: IJob) => {
                let path = '';
                switch (row?.status) {
                    case EAssignTaskStatus['PRE_INSPECTION']:
                        path = `${Slug.GR_PRE_INSPECTION}/${row?.id}?tankId=${row.tankId}&callbackUrl=${pathName}`;
                        break;
                    case EAssignTaskStatus['CONFIRMED']:
                    case EAssignTaskStatus['PREPARE']:
                    case EAssignTaskStatus['UNRESOLVED']:
                    case EAssignTaskStatus['COMPLETED']:
                        path = `${Slug.GR_NON_COMPLIANCE}/${row?.id}?tankId=${row.tankId}&callbackUrl=${pathName}`;
                        break;
                    // case EAssignTaskStatus['COMPLETED']:
                    //     path = `${Slug.GR_COMPLETE}/${row?.id}?tankId=${row.tankId}&callbackUrl=${pathName}`;
                    //     break;
                    default:
                        break;
                }
                return (
                    <Link href={path}>
                        <ChevronRight />
                    </Link>
                );
            },
        },
    ];

    const handleClick = (rowData: IJob) => {
        let path = '';
        switch (rowData?.status) {
            case EAssignTaskStatus['PRE_INSPECTION']:
                path = `${Slug.GR_PRE_INSPECTION}/${rowData?.id}?tankId=${rowData.tankId}&callbackUrl=${pathName}`;
                break;
            case EAssignTaskStatus['CONFIRMED']:
            case EAssignTaskStatus['PREPARE']:
            case EAssignTaskStatus['UNRESOLVED']:
            case EAssignTaskStatus['COMPLETED']:
                path = `${Slug.GR_NON_COMPLIANCE}/${rowData?.id}?tankId=${rowData.tankId}&callbackUrl=${pathName}`;
                break;
            // case EAssignTaskStatus['COMPLETED']:
            //     path = `${Slug.GR_COMPLETE}/${rowData?.id}?tankId=${rowData.tankId}&callbackUrl=${pathName}`;
            //     break;
            default:
                break;
        }

        router.push(path);
    };

    return (
        <Table
            dataSource={props.data}
            margin="dense"
            loading={props.loading}
            columns={columns}
            rowKey={(row) => row.id}
            layoutFixed
            loadMoreProps={{
                canLoadMore: props.canLoadMore,
                onLoadMore: props.onLoadMore,
            }}
            onClick={handleClick}
        />
    );
}
