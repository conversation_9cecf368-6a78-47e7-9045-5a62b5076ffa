import Typography from '@/components/globals/Typography';
import ETypographyTag from '@/utilities/types/enums/Typography';
import DisinfectionTable from '../DisinfectionTable';
import { flex } from '@/styled-system/patterns';
import LabReportDetail from '../LabReportDetail';
import CustomerJobSatisfied from '../CustomerJobSatisfied';
import { styles } from './styles';
import { IAssignTask } from '@/utilities/types/entities/task';
import NonCompliance from "@/components/general-report/complete-detail/DateCleanAndDisinfectionList/NonCompliance";
import PreInspection from "@/components/general-report/complete-detail/DateCleanAndDisinfectionList/PreInspection";

type Props = {
    index: number;
    assignTask: IAssignTask;
};

export default function DisinfectionHistory({ assignTask, index }: Props) {
    const classNames = styles();

    return (
        <div className={classNames.root}>
            <div className={classNames.index}>
                {index.toString().padStart(2, '0')}
            </div>
            <div className={classNames.contentContainer}>
                <Typography
                    typography="subtitle_20"
                    tag={ETypographyTag.h2}
                    color="primary_100"
                >
                    Date of Clean/Disinfection
                </Typography>
                <DisinfectionTable
                    tasks={assignTask?.tasks || []}
                    completedDate={assignTask?.completedDate}
                />
                {
                    assignTask?.nonCompliance?.tasks && assignTask?.nonCompliance?.tasks.length > 0 &&
                    <NonCompliance
                        data={assignTask?.nonCompliance?.tasks}
                        completedDate={assignTask?.nonCompliance?.date}
                    />
                }
                {
                    assignTask?.preInspection?.tasks && assignTask?.preInspection?.tasks.length > 0 &&
                    <PreInspection
                        data={assignTask?.preInspection?.tasks}
                        completedDate={assignTask?.preInspection?.date}
                    />
                }
                <div className={flex({ gap: 3, mt: 3 })}>
                    <div
                        className={flex({
                            flex: 1,
                            direction: 'column',
                        })}
                    >
                        <Typography
                            typography="subtitle_20"
                            tag={ETypographyTag.h2}
                            color="primary_100"
                        >
                            Lab Report
                        </Typography>
                        <LabReportDetail
                            labReports={assignTask?.labReports || []}
                        />
                    </div>
                    <div
                        className={flex({
                            flex: 1,
                            direction: 'column',
                        })}
                    >
                        <Typography
                            typography="subtitle_20"
                            tag={ETypographyTag.h2}
                            color="primary_100"
                        >
                            Customer job satisfied
                        </Typography>
                        <CustomerJobSatisfied
                            customerName={
                                assignTask?.tank?.location?.customer?.name || ''
                            }
                            signatureDate={assignTask?.customerSignatureDate}
                            signatureImg={assignTask?.customerSignature}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
}
