import { IPhoto } from '@/utilities/types/entities/photo';
import { SyntheticEvent, useState } from 'react';
import Modal from '../Modal';
import { ImageGridVariants, styles } from './style';
import joinClassName from '@/utilities/globals/function/className';
import ImageGallery from 'react-image-gallery';
import 'react-image-gallery/styles/css/image-gallery.css';
import './styles.css';
import ChevronLeft from '../Icons/ChevronLeft';
import ChevronRight from '../Icons/ChevronRight';

type Props = {
    photos: IPhoto[];
    maxItem?: number;
    mode?: 'view-image-gallery' | 'open-new-tab';
    className?: string;
} & ImageGridVariants;

export default function ImageGrid(props: Props) {
    const { maxItem = 2, mode = 'view-image-gallery' } = props;
    const [activeIndexSlide, setActiveIndexSlide] = useState<number | null>(
        null
    );
    const hiddenItemCount =
        props.visual === 'inline'
            ? Math.max(0, props.photos.length - maxItem)
            : 0;

    const classNames = styles({
        visual: props.visual,
    });

    const photos =
        hiddenItemCount > 0
            ? props.photos.slice(0, props.photos.length - hiddenItemCount)
            : props.photos;

    const handleClickOnImage = (e: SyntheticEvent, idx: number) => {
        if (mode !== 'view-image-gallery') return;
        e.preventDefault();
        setActiveIndexSlide(idx);
    };

    return (
        <>
            <div className={joinClassName(classNames.root, props.className)}>
                {photos?.map((photo, idx) => (
                    <a
                        className={classNames.imageContainer}
                        key={photo.key}
                        href={photo?.url || photo?.link}
                        target="__blank"
                        onClick={(e) => handleClickOnImage(e, idx)}
                    >
                        <img
                            key={photo.key}
                            src={photo?.url || photo?.link}
                            className={classNames.image}
                            alt={photo.key}
                        />
                        {idx === photos.length - 1 && hiddenItemCount > 0 ? (
                            <span className={classNames.hiddenItemCount}>
                                +{hiddenItemCount}
                            </span>
                        ) : null}
                    </a>
                ))}
            </div>
            <Modal
                gallery
                isOpen={activeIndexSlide !== null}
                onClose={() => setActiveIndexSlide(null)}
            >
                <ImageGallery
                    startIndex={activeIndexSlide || 0}
                    showPlayButton={false}
                    showFullscreenButton={false}
                    items={props.photos?.map((photo) => ({
                        original: photo?.url || photo?.link || '',
                        thumbnail: photo?.url || photo?.link || '',
                    }))}
                />
            </Modal>
        </>
    );
}
