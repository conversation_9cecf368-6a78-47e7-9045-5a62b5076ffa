'use client';

import { TableVariantType, styles } from './style';
import { UIEventHandler, useEffect, useRef } from 'react';

import TableEmpty from './TableEmpty';
import TableLoading from './TableLoading';
import _ from 'lodash';
import joinClassName from '@/utilities/globals/function/className';
import Sorting from "@/components/globals/Icons/Sorting";

export type IColProps<T> = {
    key: string;
    title: string;
    dataKey: string;
    render?: (data: T, rowIndex: number) => React.ReactNode;
    align?: 'left' | 'center' | 'right' | 'justify' | 'char';
    valign?: 'top' | 'bottom' | 'middle' | 'baseline';
    width?: string | number;
    className?: string;
    sort? : () => void;
};

export type ITableProps<T> = {
    columns: IColProps<T>[];
    rowKey: (data: T, index: number) => string;
    dataSource: T[];
    loading?: boolean;
    onClick?: (rowData: T) => void;
    loadMoreProps?: {
        canLoadMore?: boolean;
        onLoadMore?: () => void;
    };
} & TableVariantType;

export default function Table<T>(props: ITableProps<T>) {
    const containerRef = useRef<HTMLDivElement>(null);
    const isFirstLoad = !props.dataSource?.length && !!props?.loading;
    const onScroll: UIEventHandler<HTMLDivElement> = (ev) => {
        const el = ev.currentTarget;
        if (props.loadMoreProps?.canLoadMore) {
            const viewHeight = el.getBoundingClientRect().height;
            const scrollHeight = el.scrollHeight;
            if (el.scrollTop + viewHeight >= scrollHeight - 20) {
                props.loadMoreProps.onLoadMore?.();
            }
        }
    };

    const cursor = props?.onClick
        ? { cursor: 'pointer' }
        : { cursor: 'default' };

    useEffect(() => {
        if (
            props.loading ||
            !props.loadMoreProps?.canLoadMore ||
            !containerRef.current
        ) {
            return;
        }
        const containerEl = containerRef.current;
        const viewHeight = containerEl.getBoundingClientRect().height;
        const scrollHeight = containerEl.scrollHeight;

        if (viewHeight >= scrollHeight) {
            props.loadMoreProps.onLoadMore?.();
        }
    }, [
        props.loading,
        props.loadMoreProps?.canLoadMore,
        props.loadMoreProps?.onLoadMore,
        props.dataSource.length,
    ]);

    const classNames = styles({
        ...props,
    });

    return (
        <div
            ref={containerRef}
            className={classNames.container}
            onScroll={onScroll}
        >
            <table className={classNames.table}>
                <thead>
                    <tr>
                        {props.columns.map((col) => {
                            return (
                                <td
                                    className={joinClassName(
                                        classNames.th,
                                        col?.className || ''
                                    )}
                                    key={col.key}
                                    align={col.align}
                                    width={col.width}
                                >
                                    <div>
                                        <span>{col.title}</span>
                                        {
                                            col.sort && <div onClick={col.sort}><Sorting/></div>
                                        }
                                    </div>
                                </td>
                            );
                        })}
                    </tr>
                </thead>
                <tbody>
                    {props.dataSource.map((row, rowIndex) => (
                        <tr
                            className={joinClassName(
                                classNames.tr,
                                typeof (row as any)?.isActive === 'boolean' &&
                                    !(row as any)?.isActive
                                    ? 'disabled'
                                    : ''
                            )}
                            key={props.rowKey(row, rowIndex)}
                            onClick={() => props?.onClick?.(row)}
                            style={cursor}
                        >
                            {props.columns.map((col) => {
                                return (
                                    <td
                                        className={joinClassName(
                                            classNames.td,
                                            col?.className || ''
                                        )}
                                        align={col.align}
                                        valign={col.valign}
                                        key={col.key}
                                    >
                                        {col.render?.(row, rowIndex) ??
                                            _.get(row, col.dataKey)}
                                    </td>
                                );
                            })}
                        </tr>
                    ))}

                    {isFirstLoad ? (
                        <tr>
                            <td colSpan={props.columns.length}>
                                <TableLoading />
                            </td>
                        </tr>
                    ) : props.dataSource?.length === 0 ? (
                        <tr>
                            <td colSpan={props.columns.length}>
                                <TableEmpty />
                            </td>
                        </tr>
                    ) : null}
                </tbody>
            </table>
        </div>
    );
}
