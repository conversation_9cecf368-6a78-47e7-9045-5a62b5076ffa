import { RecipeVariantProps, css, sva } from '@/styled-system/css';

type Props = {
    defects: string[];
    noDefects: string[] | null;
    completed: string[] | null;
    unCompleted: string[] | null;
    overflow?: 'ellipsis' | 'clip';
} & OrderedListVariantType;

export default function OrderedList(props: Props) {
    const classNames = olStyle(props);
    return (
        <div className={css({ display: 'flex', flexDirection: 'column', gap: '4' })} >
          {
            props?.defects && props?.defects.length > 0 &&
            <div>
              <h2 className={css({fontSize: "subtitle.15 " , fontWeight: 'bold'})}>Defects</h2>
              <ol className={classNames.ol}>
                {props.defects.map((item, i) => (
                  <li key={i} className={classNames.li}>
                    {item}
                  </li>
                ))}
              </ol>
            </div>
          }
          {
            props?.noDefects && props?.noDefects?.length > 0 &&
            <div>
              <h2 className={css({fontSize: "subtitle.15 " , fontWeight: 'bold'})}>No Defects</h2>
              <ol className={classNames.ol}>
                {props.noDefects.map((item, i) => (
                  <li key={i} className={classNames.li}>
                    {item}
                  </li>
                ))}
              </ol>
            </div>
          }
          {
            props?.completed && props?.completed?.length > 0 &&
              <div>
                  <h2 className={css({fontSize: "subtitle.15 " , fontWeight: 'bold'})}>The rectified Defects</h2>
                  <ol className={classNames.ol}>
                    {props.completed.map((item, i) => (
                      <li key={i} className={classNames.li}>
                        {item}
                      </li>
                    ))}
                  </ol>
              </div>
          }
          {
            props?.unCompleted && props?.unCompleted?.length > 0 &&
              <div>
                  <h2 className={css({fontSize: "subtitle.15 " , fontWeight: 'bold'})}>The unresolve Defects</h2>
                  <ol className={classNames.ol}>
                    {props.unCompleted.map((item, i) => (
                      <li key={i} className={classNames.li}>
                        {item}
                      </li>
                    ))}
                  </ol>
              </div>
          }
        </div>
    );
}

const olStyle = sva({
    slots: ['ol', 'li'],
    base: {
        ol: {
            listStyle: 'inside decimal',
        },
    },
    variants: {
        overflow: {
            ellipsis: {
                li: {
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                },
            },
            clip: {
                li: {
                    overflow: 'hidden',
                    textOverflow: 'clip',
                    whiteSpace: 'nowrap',
                },
            },
        },
    },
});

type OrderedListVariantType = RecipeVariantProps<typeof olStyle>;
