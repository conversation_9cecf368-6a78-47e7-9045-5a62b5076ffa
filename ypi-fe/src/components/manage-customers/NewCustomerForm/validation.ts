import { phoneValidator } from '@/utilities/validations';
import { z } from 'zod';

const newSchema = {
    email: z.string()
        .optional()
        .superRefine((val, ctx) => {
            if (val && !z.string().email().safeParse(val).success) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    message: 'Invalid email address',
                });
            }
        }),
    pubNumber: z.string().optional(),
    nameBuilding: z.string().optional(),
    customerName: z.string().optional(),
    buildingCategory: z.string().optional(),
    contactPerson: z.string()
        .optional()
        .superRefine((val, ctx) => {
            if (val && !phoneValidator(val)) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    message: 'Invalid phone number',
                });
            }
        }),
    officeTel: z.string()
        .optional()
        .superRefine((val, ctx) => {
            if (val && !phoneValidator(val)) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    message: 'Invalid phone number',
                });
            }
        }),
};

export const createNewCustomerSchema = z.object(newSchema);

export const createNewCustomerWithAgentSchema = z.object({
    ...newSchema,
    contactName: z.string().optional(),
    companyName: z.string().optional(),
    designation: z.string().optional(),
    inChargeCategory: z.string().optional(),
    postalCode: z.string().optional(),
    blockNo: z.string().optional(),
    street: z.string().optional(),
    buildingName: z.string().optional(),
    agentOfficeTel: z.string()
        .optional()
        .superRefine((val, ctx) => {
            if (val && !phoneValidator(val)) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    message: 'Invalid phone number',
                });
            }
        }),
    agentEmail: z.string()
        .optional()
        .superRefine((val, ctx) => {
            if (val && !z.string().email().safeParse(val).success) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    message: 'Invalid email address',
                });
            }
        }),
});
