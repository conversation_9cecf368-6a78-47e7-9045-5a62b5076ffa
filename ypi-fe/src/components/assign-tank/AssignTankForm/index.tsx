import Button from '@/components/globals/Button';
import {
    FieldValues,
    useForm,
    UseFormHandleSubmit,
    UseFormSetError,
} from 'react-hook-form';
import { assignTankResolver } from './validation';

import { FormPart } from '@/components/globals/InputForm/FormPart';
import { useCreateAssignTank, useUpdateAssignTank } from '@/hooks/tank';
import CommonMessages from '@/utilities/messages/common';
import { EFormInputType } from '@/utilities/types/form';
import { useRouter, useSearchParams } from 'next/navigation';
import toast from 'react-hot-toast';
import { bottomContainerStyle, formPartStyle, formStyle } from './style';
import { Slug } from '@/utilities/types/enums/Slug';
import { ITank } from '@/utilities/types/entities/tank';
import { dayjs } from '@/utilities/globals/function/dateHelper';
import { TMultiSelectOption } from '@/components/globals/MultiSelect/type';
import { useEffect } from 'react';
import { EActionTypes } from '@/utilities/types/enums/Form';
import { css } from '@/styled-system/css';

type TFormData = {
    staffs: string[];
    timeLine: string;
};
type TAssignTankProps = {
    disabled?: boolean;
    defaultValues?: Partial<TFormData>;
    hasAgent?: boolean;
    submitText?: string;
    mode?: 'edit' | 'create';
    renderHeader?: ({
        onUpdate,
        setError,
    }: {
        setError: UseFormSetError<FieldValues>;
        onUpdate: UseFormHandleSubmit<FieldValues>;
    }) => JSX.Element;
    tank?: ITank;
    workerOptions?: TMultiSelectOption[];
};

export function AssignTankForm({
    defaultValues = {},
    renderHeader,
    mode,
    submitText,
    tank,
    workerOptions,
}: TAssignTankProps) {
    const { trigger: onCreateAssignTask, isMutating: isCreating } =
        useCreateAssignTank();

    const { trigger: onUpdateAssignTank, isMutating: isUpdating } =
        useUpdateAssignTank(tank?.assignTask?.id || '');
    const searchParams = useSearchParams();
    const router = useRouter();

    const callbackUrl = searchParams.get('callbackUrl');
    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
        control,
        setError,
        reset,
    } = useForm({
        defaultValues: defaultValues,
        resolver: assignTankResolver,
    });

    const onSubmit = (value: FieldValues) => {
        const tankDate = dayjs(value.timeLine).toISOString();

        const payload = {
            tankId: tank?.id,
            startAt: tankDate,
            endAt: tankDate,
            staffs: value.staffs,
        };

        if (tank?.assignTask?.id) {
            toast
                .promise(onUpdateAssignTank(payload), {
                    loading: CommonMessages.Updating,
                    error: (err) => err.message,
                    success: () => {
                        if (callbackUrl) {
                            router.push(callbackUrl);
                        } else {
                            router.back();
                        }
                        return CommonMessages.UpdatedSuccessful;
                    },
                })
                .then((re) => {
                    console.log(re);
                    // router.push(Slug.MANAGE_STAFF);
                })
                .catch((err) => {
                    console.log(err);
                });
        } else {
            toast
                .promise(onCreateAssignTask(payload), {
                    loading: CommonMessages.Creating,
                    error: (err) => err.message,
                    success: (response) => {
                        if (response?.error) {
                            toast.error(response.msg || 'An error occurred');
                            return null;
                        }

                        if (callbackUrl) {
                            router.push(callbackUrl);
                        } else {
                            router.back();
                        }
                        return CommonMessages.CreatedSuccessful;
                    },
                })
                .then((re) => {
                    console.log(re);
                    // router.push(Slug.MANAGE_STAFF);
                })
                .catch((err) => {
                    console.log(err);
                });
        }
    };

    const onBack = () => {
        if (callbackUrl) {
            router.push(callbackUrl);
        }

        router.back();
    };

    useEffect(() => {
        if (tank) {
            reset({
                timeLine: tank?.assignTask?.startAt,
                staffs: tank?.assignTask?.staffs?.map((item) => item?.id) || [],
            });
        }
    }, [tank]);

    return (
        <form className={formStyle} onSubmit={handleSubmit(onSubmit)}>
            {renderHeader?.({
                onUpdate: handleSubmit(onSubmit) as any,
                setError,
            })}
            <FormPart
                inputs={[
                    {
                        id: 'timeLine',
                        name: 'timeLine',
                        label: 'Timeline',
                        type: EFormInputType.DatePicker,
                        variant: 'contained',
                        labelPosition: 'left',
                        showTimeSelect: true,
                        timeFormat: 'HH:mm',
                        timeIntervals: 15,
                        timeCaption: 'time',
                        dateFormat: 'HH:mm dd/MM/yyyy',
                        placeholderText: 'HH:MM DD/MM/YYYY',
                        suffixIconClassName: css({ color: 'secondary.60' }),
                        minDate: new Date(),
                    },
                    {
                        id: 'staffs',
                        name: 'staffs',
                        label: 'Staffs',
                        type: EFormInputType.MultiSelect,
                        variant: 'contained',
                        labelPosition: 'left',
                        placeholder: 'Select Staff',
                        options: workerOptions,
                    },
                ]}
                control={control}
                errors={errors}
                register={register}
                setValue={setValue}
                className={formPartStyle}
            />
            {mode !== EActionTypes['EDIT'] && (
                <div className={bottomContainerStyle}>
                    <Button
                        type="reset"
                        onClick={() => {
                            router.back();
                        }}
                        visual="outline_secondary_60"
                        disabled={isCreating}
                    >
                        Cancel
                    </Button>
                    <Button disabled={isCreating}>
                        {submitText ?? 'Create'}
                    </Button>
                </div>
            )}

            {mode === EActionTypes['EDIT'] && (
                <div className={bottomContainerStyle}>
                    <Button
                        type="reset"
                        onClick={onBack}
                        visual="outline_secondary_60"
                        disabled={isUpdating}
                    >
                        Cancel
                    </Button>
                    <Button
                        disabled={
                            isUpdating || !!tank?.assignTask?.isStaffSubmitted
                        }
                    >
                        Update
                    </Button>
                </div>
            )}
        </form>
    );
}
