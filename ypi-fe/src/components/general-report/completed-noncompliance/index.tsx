'use client';

import { useEffect, useMemo, useState } from 'react';
import toast from 'react-hot-toast';
import ScrollView from '@/components/globals/ScrollView';
import TopBar from '@/components/manage-customers/TopBar';
import { flex } from '@/styled-system/patterns';
import { useRouter } from 'next/navigation';
import { css } from '@/styled-system/css';
import Button from '@/components/globals/Button';
import { emailIconStyle, footerWrapper, scrollViewContainer } from './style';
import { IStaff } from '@/utilities/types/entities/staff';
import NonComplianceHeader from '@/components/general-report/non-compliance-detail/Header';
import NonComplianceTaskList from '@/components/general-report/non-compliance-detail/TaskList';
import CommonMessages from '@/utilities/messages/common';
import { Slug } from '@/utilities/types/enums/Slug';
import { useTaskConfirm } from '@/hooks/assign-task';
import { INonComplianceDetail } from '@/utilities/types/entities/general-report';
import { MailIcon } from '@/components/globals/Icons';
import { SendEmailModal } from '../non-compliance-detail/SendEmailModal';
import JobProgress, {
    EJobProgressStep,
} from '@/components/manage-customers/JobProgress';
import DownloadIcon from '@/components/globals/Icons/DownloadIcon';
import { useDownloadEmailContent } from '@/hooks/general-report';
import { EGeneralReport } from '@/utilities/types/enums/GeneralReport';
import { downloadFileBlob } from '@/utilities/globals/function/download';

type CompletedNonComplianceDetailProps = {
    data?: INonComplianceDetail;
    params: { id: string };
    searchParams: { [key: string]: string | string[] | undefined };
};

export default function CompletedNonComplianceDetailPage({
    data,
    searchParams,
}: CompletedNonComplianceDetailProps) {
    const { trigger, isMutating } = useTaskConfirm();
    const router = useRouter();
    const [taskIds, setTaskIds] = useState<string[]>([]);
    const [open, setOpen] = useState(false);

    const staffNames = useMemo(() => {
        if (!data || !data?.tasks?.length) return [];
        let staffs: IStaff[] = [];
        for (const task of data?.tasks) {
            if (!task?.staffs?.length) continue;
            staffs = [...staffs, ...task?.staffs];
        }
        return [...new Set(staffs.map((item) => item?.fullName))];
    }, [data]);

    const handleCheckboxChange = (taskId: string, checked: boolean) => {
        // if (checked) {
        //     setTaskIds((prevTaskIds) => [...prevTaskIds, taskId]);
        //     return;
        // }
        // setTaskIds((prevTaskIds) => prevTaskIds.filter((id) => id !== taskId));
    };

    const handleConfirm = () => {
        if (!data?.id) return;
        toast
            .promise(trigger({ assignTaskId: data?.id }), {
                loading: CommonMessages.Confirm,
                success: CommonMessages.ConfirmSuccessful,
                error: (err) => err.message,
            })
            .then(() => {
                router.push(`${Slug.GR_COMPLETE}/${data?.id}`);
            });
    };

    const handleClickSendEmail = () => {
        setOpen(!open);
    };

    const { trigger: downloadEmail, isMutating: isMutatingDownload } =
        useDownloadEmailContent(EGeneralReport.NON_COMPLIANCE_DOWNLOAD_EMAIL);

    async function downloadPdf() {
        const response = await downloadEmail({
            assignTaskId: data?.id || '',
        });

        await downloadFileBlob({
            file: response,
            filename: 'NoneComplianceEmailContent',
        });
    }
    useEffect(() => {
        if (Array.isArray(data?.tasks)) {
            const temp =
                data?.tasks
                    ?.filter((item) => !!item.selected)
                    .map((item) => item.id) || [];
            setTaskIds([...temp]);
        }
    }, [data?.tasks]);

    if (!data) return null;
    return (
        <>
            <div className={flex({ flex: 1, direction: 'column' })}>
                <TopBar
                    onBack={() => {
                        router.back();
                    }}
                    title={`Job ID`}
                    subTitle={data?.code}
                    primaryButtonVisible={false}
                    renderButtons={() =>
                        searchParams?.tankId && (
                            <JobProgress
                                activeStep={
                                data?.isConfirmed ? EJobProgressStep['Complete'] :
                                    EJobProgressStep['RectifyNonCompliance']
                                }
                            />
                        )
                    }
                />
                <ScrollView className={scrollViewContainer}>
                    <div className={css({ mb: 3 })}>
                        <NonComplianceHeader
                            nonComplianceDate={data?.startAt}
                            tank={data?.tank}
                            location={data?.tank?.location}
                            staffNames={staffNames}
                        />
                    </div>
                    <NonComplianceTaskList
                        tasks={data?.tasks.filter((item) => item.selected)}
                        selectedTaskIds={taskIds}
                        onCheckbox={handleCheckboxChange}
                    />
                </ScrollView>
                <div className={footerWrapper}>
                    <Button
                        size="medium"
                        type="button"
                        disabled={isMutatingDownload}
                        onClick={async () => {
                            toast.promise(downloadPdf(), {
                                loading: CommonMessages.Downloading,
                                success: () => {
                                    return CommonMessages.DownloadSuccessful;
                                },
                                error: (err) => err.message,
                            });
                        }}
                    >
                        <DownloadIcon
                            width={18}
                            height={18}
                            className={css({
                                color: 'white',
                            })}
                        />
                        <span>Download</span>
                    </Button>
                    <Button
                        onClick={handleClickSendEmail}
                        size="medium"
                        type="button"
                        visual="outline_primary"
                    >
                        <MailIcon
                            width={18}
                            height={18}
                            className={emailIconStyle}
                        />
                        <span>Send email</span>
                    </Button>
                    <Button disabled={isMutating} onClick={handleConfirm}>
                        Confirm
                    </Button>
                </div>
            </div>
            <SendEmailModal
                isOpen={open}
                onClose={handleClickSendEmail}
                tank={data?.tank}
                location={data?.tank?.location}
                staffs={data?.staffs}
                tasks={data?.tasks}
                completedDate={data?.createdAt}
                assignTaskId={data?.id}
            />
        </>
    );
}
