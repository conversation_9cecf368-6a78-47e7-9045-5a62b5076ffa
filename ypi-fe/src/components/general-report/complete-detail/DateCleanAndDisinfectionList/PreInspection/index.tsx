import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import AnimateHeight from 'react-animate-height';
import ChevronDown from '@/components/globals/Icons/ChevronDown';
import Typography from '@/components/globals/Typography';
import { ITask } from '@/utilities/types/entities/task';
import {taleStyles} from "@/components/tank-detail/DisinfectionTable/style";

interface Props {
    data: ITask[];
    completedDate: string;
    loading?: boolean;
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
}

export default function PreInspection({
    data,
    completedDate,
}: Props) {
    const tableRef = useRef<HTMLTableElement | null>(null);
    const [collapsed, setCollapsed] = useState(true);
    const [height, setHeight] = useState<'auto' | number>('auto');
    const [localData, setLocalData] = useState<ITask[]>([]);
    const classNames = taleStyles({ collapsed });
    useEffect(() => {
        let t: number | undefined;
        function updateHeight() {
            if (t) {
                cancelAnimationFrame(t);
            }
            t = requestAnimationFrame(() => {
                const height = tableRef.current?.getBoundingClientRect().height;
                setHeight((height ?? 0) + 48);
            });
        }
        updateHeight();
        window.addEventListener('resize', updateHeight);

        return () => {
            if (t) {
                cancelAnimationFrame(t);
            }
            window.removeEventListener('resize', updateHeight);
        };
    }, [collapsed, height]);

    useEffect(() => {
        setLocalData(data);
    }, [data]);

    return (
        <div className={classNames.root}>
            <AnimateHeight
                contentClassName={classNames.container}
                height={height}
                duration={300}
            >
                <table className={classNames.table} ref={tableRef}>
                    <thead className={classNames.thead}>
                        <tr>
                            <th align="left">
                                <Typography
                                    typography="subtitle_15"
                                    color="secondary_100"
                                >
                                    Pre-inspection
                                </Typography>
                            </th>
                            <th align="left">
                                <Typography
                                    typography="subtitle_15"
                                    color="secondary_100"
                                >
                                    No-defects
                                </Typography>
                            </th>
                        </tr>
                    </thead>
                    <tbody className={classNames.tbody}>
                        {localData.map((item, index) => {
                            return (
                                <tr key={`${item?.id}_${index}`}>
                                    {index === 0 ? (
                                        <>
                                            <td
                                                width={170}
                                                rowSpan={data?.length}
                                            >
                                                {completedDate
                                                    ? dayjs(
                                                          completedDate
                                                      ).format('DD/MM/YYYY')
                                                    : ''}
                                            </td>
                                        </>
                                    ) : null}
                                    <td>{`${index + 1}.  ${item?.title}`}</td>
                                </tr>
                            );
                        })}
                    </tbody>
                </table>
                <div className={classNames.footerPlaceholder} />
                <div
                    className={classNames.footer}
                    onClick={() => {
                        setCollapsed(!collapsed);
                    }}
                >
                    {collapsed ? 'View more' : 'View less'}
                    <ChevronDown />
                </div>
            </AnimateHeight>
        </div>
    );
}
