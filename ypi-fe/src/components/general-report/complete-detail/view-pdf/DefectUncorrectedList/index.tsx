import { titleStyle } from './style';
import Typography from '@/components/globals/Typography';
import { flex } from '@/styled-system/patterns';
import DefectTable from '../DefectTable';
import { ITask } from '@/utilities/types/entities/task';

interface DefectUncorrectedListProps {
    data: ITask[];
}

export default function DefectUncorrectedList({
    data,
}: DefectUncorrectedListProps) {
    return (
        <div className={flex({ direction: 'column', gap: '16px' })}>
            <div className={titleStyle}>
                <Typography typography="header_20" color="primary_100">
                    The unresolve Defects
                </Typography>
            </div>
            <DefectTable data={data} />
        </div>
    );
}
