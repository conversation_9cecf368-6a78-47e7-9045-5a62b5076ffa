
import Typography from '@/components/globals/Typography';
import {flex} from '@/styled-system/patterns';
import DefectTable from '../DefectTable';
import {ITask} from '@/utilities/types/entities/task';
import {titleStyle} from "@/components/general-report/complete-detail/view-pdf/NoDefects/style";

interface NoDefectsListProps {
    data: ITask[];
}

export default function NoDefectsList({data}: NoDefectsListProps) {
    return (
        <div className={flex({direction: 'column', gap: '16px'})}>
            <div className={titleStyle}>
                <Typography typography="header_20" color="primary_100">
                    No-defects
                </Typography>
            </div>
            <DefectTable data={data}/>
        </div>
    );
}