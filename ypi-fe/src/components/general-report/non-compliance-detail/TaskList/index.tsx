import { IAssignTask, ITask } from '@/utilities/types/entities/task';
import ImageGrid from '@/components/globals/ImageGrid';
import Table, { IColProps } from '@/components/globals/Table';
import Typography from '@/components/globals/Typography';
import { css } from '@/styled-system/css';
import ETypographyTag from '@/utilities/types/enums/Typography';
import { photoStyle, staffRemarkLabelStyle, staffRemarkStyle } from './style';
import Checkbox from '@/components/globals/Checkbox';

export interface NonComplianceTaskListProps {
    tasks: ITask[];
    canLoadMore?: boolean | undefined;
    onLoadMore?: (() => void) | undefined;
    loading?: boolean;
    selectedTaskIds: string[];
    onCheckbox?: (taskId: string, checked: boolean) => void;
}

export default function NonComplianceTaskList({
    tasks,
    loading,
    canLoadMore,
    onLoadMore,
    onCheckbox,
    selectedTaskIds,
}: NonComplianceTaskListProps) {
    const columns: IColProps<ITask>[] = [
        {
            dataKey: 'checkbox',
            title: '',
            key: 'checkbox',
            render: (data) => {
                return (
                    <Checkbox
                        isChecked={selectedTaskIds?.includes(data?.id || '')}
                        size="small"
                        onChange={(checked: boolean) =>
                            onCheckbox?.(data?.id, checked)
                        }
                    />
                );
            },
            width: 18,
        },
        {
            dataKey: '',
            title: '',
            key: 'ordinalNumber',
            align: 'left',
            width: 10,
            render: (_, rowIndex) => {
                const index = rowIndex + 1;
                if (index < 10) return `0${index}`;
                return index;
            },
        },
        {
            dataKey: 'title',
            title: 'The rectified Defects',
            key: 'title',
            align: 'left',
            width: 327,
            render: (data) => {
                return (
                    <>
                        <Typography
                            color="body_1"
                            typography="body_15"
                            tag={ETypographyTag.h1}
                            className={css({
                                whiteSpace: 'pre-wrap',
                            })}
                        >
                            {data?.title || ''}
                        </Typography>
                        {data?.before?.staffRemark ? (
                            <Typography
                                color="body_1"
                                typography="body_15"
                                tag={ETypographyTag.h1}
                                className={staffRemarkStyle}
                            >
                                <span className={staffRemarkLabelStyle}>
                                    Remark:
                                </span>
                                {data?.before?.staffRemark || ''}
                            </Typography>
                        ) : null}
                    </>
                );
            },
        },
        {
            dataKey: 'qty',
            title: 'Qty',
            key: 'qty',
            align: 'left',
        },
        {
            dataKey: 'size',
            title: 'Size',
            key: 'size',
            align: 'left',
        },
        {
            dataKey: 'photo',
            title: 'Photo (Before)',
            key: 'photos_before_non_compliance',
            align: 'left',
            width: 160,
            className: photoStyle,
            render: (data) => {
                return (
                    <ImageGrid
                        photos={
                            data?.before?.images?.map((image) => ({
                                id: image?.id || '',
                                url: image?.link || '',
                                key: image?.name || '',
                                link: image?.link || '',
                            })) || []
                        }
                        visual="inline"
                        maxItem={2}
                        mode="view-image-gallery"
                        className={css({ pt: 0 })}
                    />
                );
            },
        },
        {
            dataKey: 'photo',
            title: 'Photo (After)',
            key: 'photos_after_non_compliance',
            align: 'left',
            width: 160,
            className: photoStyle,
            render: (data) => {
                return (
                    <ImageGrid
                        photos={
                            data?.images?.map((image) => ({
                                id: image?.id || '',
                                url: image?.link || '',
                                key: image?.name || '',
                                link: image?.link || '',
                            })) || []
                        }
                        visual="inline"
                        maxItem={2}
                        mode="view-image-gallery"
                        className={css({ pt: 0 })}
                    />
                );
            },
        },
        {
            dataKey: 'staffRemark',
            title: 'Remark',
            key: 'staffRemark',
            align: 'left',
            width: 200,
            render: (data) => {
                return (
                    <Typography
                        color="body_1"
                        typography="body_15"
                        tag={ETypographyTag.h1}
                        className={css({ whiteSpace: 'pre-wrap' })}
                    >
                        {data?.staffRemark || ''}
                    </Typography>
                );
            },
        },
        {
            dataKey: 'staffs',
            title: 'Staffs',
            key: 'staffs',
            align: 'left',
            width: 200,
            render: (data) => {
                let staffNames: string[] = [];
                if (data?.staffs?.length) {
                    staffNames = data?.staffs.map((item) => item?.fullName);
                }
                return (
                    <Typography
                        color="body_1"
                        typography="body_15"
                        tag={ETypographyTag.h1}
                        className={css({ whiteSpace: 'pre-wrap' })}
                    >
                        {staffNames.join(', ')}
                    </Typography>
                );
            },
        },
        // {
        //     dataKey: 'checkbox',
        //     title: '',
        //     key: 'checkbox',
        //     render: (data) => (
        //         <Checkbox
        //             size="small"
        //             onChange={(e) => {
        //                 onCheckbox?.(data?.id, e);
        //             }}
        //         />
        //     ),
        //     width: 18,
        // },
    ];

    return (
        <Table
            dataSource={tasks}
            columns={columns}
            loading={loading}
            rowKey={(row) => row.id}
            loadMoreProps={{
                canLoadMore: canLoadMore,
                onLoadMore: onLoadMore,
            }}
            margin="dense"
        />
    );
}
