import { EAssignTaskStatus } from '@/utilities/types/enums/Task';

export const ASSIGN_TASK_STATUS_LABELS: Record<
    EAssignTaskStatus,
    { label: string; color: string }
> = {
    [EAssignTaskStatus['PREPARE']]: {
        label: 'Prepare',
        color: 'secondary_60',
    },

    [EAssignTaskStatus['PRE_INSPECTION']]: {
        label: 'Pre-inspection',
        color: 'yellow_100',
    },
    [EAssignTaskStatus['CONFIRMED']]: {
        label: 'Confirmed',
        color: 'success_100',
    },
    [EAssignTaskStatus['COMPLETED']]: {
        label: 'Completed',
        color: 'primary_100',
    },
    [EAssignTaskStatus['UNRESOLVED']]: {
        label: 'Unresolved',
        color: 'warning_100',
    },
};

export const CATEGORY_ASSIGN_TASK_LABELS: Record<
    EAssignTaskStatus,
    { label: string; color: string }
> = {
    [EAssignTaskStatus['PREPARE']]: {
        label: 'Rectify',
        color: 'primary_100',
    },

    [EAssignTaskStatus['PRE_INSPECTION']]: {
        label: 'Pre-Inspection',
        color: 'yellow_100',
    },
    [EAssignTaskStatus['CONFIRMED']]: {
        label: 'Completed',
        color: 'success_100',
    },
    [EAssignTaskStatus['COMPLETED']]: {
        label: 'Rectify',
        color: 'primary_100',
    },
    [EAssignTaskStatus['UNRESOLVED']]: {
        label: 'Unresolved',
        color: 'warning_100',
    },
};
