import { EAssignTaskStatus } from '../enums/Task';
import { IPhoto } from './photo';

export interface IJob {
    id: string;
    code?: string;
    status?: EAssignTaskStatus;
    address?: string;
    tankId?: string;
    tankAirdentity?: string;
    staff?: string;
    isStaffSubmitted?: boolean;
    isConfirmed?: boolean;
    photos?: IPhoto[] | null;
    completedAt?: string | null;
    defects?: string[];
    noDefects?: string[];
    inspector?: string | null;
    createdAt?: string;
    remark?: string;
    takenAt?: string;
    startedAt?: string;
    originalStart?: string;
    originalEnd?: string;
    completed?: string[];
    unCompleted? : string[];
}
