import { EventItem } from '@/components/globals/BigCalendar/types';
import { IAssignTask } from '../types/entities/task';
import { IJob } from '../types/entities/job';
import dayjs from 'dayjs';
import {EAssignTaskStatus, ETaskStatus} from '../types/enums/Task';
import { FORMAT } from '@/configs/date';

export const convertTaskToEventItem = (
    assignTasks: IAssignTask[]
): EventItem<IJob>[] => {
    // Group tasks by day
    const tasksByDay: { [key: string]: IAssignTask[] } = {};
    assignTasks.forEach((task) => {
        const day = dayjs(task.startAt).format('YYYY-MM-DD');
        if (!tasksByDay[day]) {
            tasksByDay[day] = [];
        }
        tasksByDay[day].push(task);
    });

    // Initialize y position for stacking
    const yPosByDay: { [key: string]: number } = {};

    // Process tasks for each day
    const eventItems: EventItem<IJob>[] = [];
    Object.keys(tasksByDay).forEach((day) => {
        const tasks = tasksByDay[day];

        // Sort tasks by start time within each day
        tasks.sort(
            (a, b) => dayjs(a.startAt).valueOf() - dayjs(b.startAt).valueOf()
        );

        let yPos = yPosByDay[day] || 0;

        // Convert tasks to EventItems
        tasks.forEach((assignTask) => {
            // Calculate start and end times based on position
            const start = dayjs(assignTask.startAt)
                .startOf('day')
                .add(yPos, 'hours')
                .toDate();
            const end = dayjs(start).add(1, 'hours').toDate();

            const originalStart = dayjs(assignTask.startAt).format(
                FORMAT.DATE_TIME_FULL
            );
            const originalEnd = dayjs(assignTask.startAt)
                .add(1, 'hours')
                .format(FORMAT.DATE_TIME_FULL);

            // Increment y position for the next task
            yPos += 1;

            // Create EventItem
            eventItems.push({
                start,
                end,
                data: {
                    originalStart,
                    originalEnd,
                    id: assignTask.id || '',
                    code: assignTask.code || '',
                    inspector: '',
                    status:
                        assignTask.status ||
                        EAssignTaskStatus['PRE_INSPECTION'],
                    address: `${assignTask.tank?.location?.street} ${assignTask.tank?.location?.blockNo} ${assignTask.tank?.location?.building}`,
                    tankId: assignTask.tank?.id || assignTask.tankId || '',
                    tankAirdentity: assignTask.tank?.airdentity || '',
                    isStaffSubmitted: assignTask.isStaffSubmitted,
                    isConfirmed: assignTask.isConfirmed,
                    staff:
                        assignTask.staffs
                            ?.map((staff) => staff.fullName)
                            .join(', ') || '',
                },
                resourceId: 1,
            });
        });

        // Update yPosByDay for the next day
        yPosByDay[day] = yPos;
    });

    return eventItems;
};

export const convertTaskToJob = (assignTasks: IAssignTask[]): IJob[] =>
    assignTasks.map((assignTask) => ({
        id: assignTask?.id || '',
        code: assignTask?.code || '',
        inspector: '',
        status: assignTask?.status || EAssignTaskStatus['PRE_INSPECTION'],
        address: `${assignTask?.tank?.location?.street} ${assignTask?.tank?.location?.blockNo} ${assignTask?.tank?.location?.building}`,
        tankId: assignTask?.tank?.id || assignTask?.tankId || '',
        tankAirdentity: assignTask?.tank?.airdentity || '',
        isStaffSubmitted: assignTask?.isStaffSubmitted,
        isConfirmed: assignTask?.isConfirmed,
        staff: assignTask?.staffs?.map((staff) => staff?.fullName)?.join(', ') || '',
        defects: assignTask?.tasks?.filter(task => task?.status === ETaskStatus.RECTIFY).map(task => task?.title) || [],
        noDefects: assignTask?.tasks?.filter(task => task?.status === ETaskStatus.NEW).map(task => task?.title) || [],
        completed: assignTask?.tasks?.filter(task => task?.status === ETaskStatus.COMPLETED ).map(task => task?.title) || [],
        unCompleted: assignTask?.tasks?.filter(task => task?.status === ETaskStatus.UN_COMPLETED).map(task => task?.title) || [],
        startedAt: assignTask?.startAt || '',
        completedAt: assignTask?.completedDate || '',
    }));
