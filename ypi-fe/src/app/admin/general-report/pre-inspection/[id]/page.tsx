'use client';

import { useEffect, useState } from 'react';
import { emailIconStyle, footerWrapper, scrollViewContainer } from './style';
import Button from '@/components/globals/Button';
import Link from 'next/link';
import PreInspectionHeader from '@/components/general-report/pre-inspection-detail/PreInspectionHeader';
import ScrollView from '@/components/globals/ScrollView';
import TaskList from '@/components/tasks/TaskList';
import TopBar from '@/components/manage-customers/TopBar';
import { css } from '@/styled-system/css';
import { flex } from '@/styled-system/patterns';
import {
    useDefect,
    useDownloadEmailContent,
    usePreInspectionId,
} from '@/hooks/general-report';
import { useRouter } from 'next/navigation';
import { Slug } from '@/utilities/types/enums/Slug';
import { MailIcon } from '@/components/globals/Icons';
import { SendEmailModal } from '@/components/general-report/pre-inspection-detail/SendEmailModal';
import JobProgress, {
    EJobProgressStep,
} from '@/components/manage-customers/JobProgress';
import DownloadIcon from '@/components/globals/Icons/DownloadIcon';
import {EAssignTaskStatus, ETaskStatus} from '@/utilities/types/enums/Task';
import { EGeneralReport } from '@/utilities/types/enums/GeneralReport';
import { downloadFileBlob } from '@/utilities/globals/function/download';
import toast from 'react-hot-toast';
import CommonMessages from '@/utilities/messages/common';
import Modal from "@/components/globals/Modal";
import DefectForm from "@/components/manage-customers/DefectForm";

type TProps = {
    params: { id: string };
    searchParams: { callbackUrl: string } & {
        [key: string]: string | string[] | undefined;
    };
};

export default function PreInspectionDetailPage({
    params,
    searchParams,
}: TProps) {
    const router = useRouter();
    const { data } = usePreInspectionId(params.id);
    const { getDefectIds, setDefectId, removeAllDefectIds } = useDefect();
    const [checkboxes, setCheckboxes] = useState<string[]>([]);
    const [open, setOpen] = useState(false);
    const [createDefect, setCreateDefect] = useState(false);

    useEffect(() => {
        removeAllDefectIds();
    }, []);

    const handleClick = (defectId: string) => {
        setDefectId(defectId);
        setCheckboxes(getDefectIds());
    };
  useEffect(() => {
    if (Array.isArray(data?.tasks)) {
      removeAllDefectIds();

      const rectifyDefectIds = data?.tasks
        ?.filter((item) => item.status === ETaskStatus['RECTIFY'])
        .map((item) => item?.defect?.id || '')
        .filter(id => id !== '');

      setCheckboxes(rectifyDefectIds || []);

      rectifyDefectIds.forEach(defectId => {
        setDefectId(defectId);
      });
    }
  }, [data]);

  const handleClickSendEmail = () => {
        setOpen(!open);
    };
    const { trigger: downloadEmail, isMutating } = useDownloadEmailContent(
        EGeneralReport.PRE_INSPECTION_DOWNLOAD_EMAIL
    );

    async function downloadPdf() {
        const response = await downloadEmail({
            assignTaskId: data?.id || '',
        });

        await downloadFileBlob({
            file: response,
            filename: 'preInspectionEmailContent',
        });
    }
    if (!data) return null;

  const dataDefects = data.tasks.filter(item =>
    item.defect && checkboxes.includes(item.defect.id)
  );

  const dataNoDefects = data.tasks.filter(item =>
    !item.defect || !checkboxes.includes(item.defect.id)
  );

  return (
        <>
            <div
                className={flex({
                    flex: 1,
                    direction: 'column',
                })}
            >
                <TopBar
                    onBack={() => {
                        router.back();
                    }}
                    title={`Job ID`}
                    subTitle={data?.code}
                    primaryButtonVisible={false}
                    renderButtons={() =>
                        searchParams?.tankId && (
                            <JobProgress
                                activeStep={
                                    EJobProgressStep['PreInspectionList']
                                }
                            />
                        )
                    }
                />

                <ScrollView className={scrollViewContainer}>
                    <div
                        className={css({
                            mb: 3,
                        })}
                    >
                        <PreInspectionHeader
                            preInspectionDate={data.createdAt}
                            tank={data.tank}
                            location={data.tank.location}
                            staffs={data.staffs}
                        />
                    </div>

                    <TaskList
                      data={dataDefects}
                      selectedTaskIds={checkboxes}
                      handleClick={(defectId) => {
                        handleClick(defectId);
                      }}
                      title = "Defects"
                    />
                    <TaskList
                      data={dataNoDefects}
                      selectedTaskIds={checkboxes}
                      handleClick={(defectId) => {
                        handleClick(defectId);
                      }}
                      title = "No Defects"
                    />
                </ScrollView>
                <div className={footerWrapper}>
                    <Button
                        size="medium"
                        type="button"
                        onClick = {() => setCreateDefect(true)}
                    >
                        Add Defect
                    </Button>
                    <Button
                        size="medium"
                        type="button"
                        disabled={isMutating}
                        onClick={async () => {
                            toast.promise(downloadPdf(), {
                                loading: CommonMessages.Downloading,
                                success: () => {
                                    return CommonMessages.DownloadSuccessful;
                                },
                                error: (err) => err.message,
                            });
                        }}
                    >
                        <DownloadIcon
                            width={18}
                            height={18}
                            className={css({
                                color: 'white',
                            })}
                        />
                        <span>Download</span>
                    </Button>
                    <Button
                        onClick={handleClickSendEmail}
                        size="medium"
                        type="button"
                        visual="outline_primary"
                    >
                        <MailIcon
                            width={18}
                            height={18}
                            className={emailIconStyle}
                        />
                        <span>Send email</span>
                    </Button>
                    <Link
                        href={`${Slug.GR_PRE_INSPECTION}/${
                            data.id
                        }/assign-staff?callbackUrl=${
                            searchParams?.callbackUrl || Slug.GR_PRE_INSPECTION
                        }`}
                    >
                        <Button disabled={checkboxes.length === 0}>
                            Assign staff
                        </Button>
                    </Link>
                </div>
            </div>
            <SendEmailModal
                isOpen={open}
                onClose={handleClickSendEmail}
                tank={data?.tank}
                location={data?.tank?.location}
                staffs={data?.staffs}
                tasks={data?.tasks}
                preInspectionDate={data?.createdAt}
                assignTaskId={data?.id}
                status={data?.status as EAssignTaskStatus}
            />
            <Modal
                isOpen={createDefect}
                onClose={() => setCreateDefect(false)}
                title={'Create Defect'}
                sizes="sm"
            >
               <DefectForm setCreateDefect={setCreateDefect}/>
            </Modal>
        </>
    );
}
