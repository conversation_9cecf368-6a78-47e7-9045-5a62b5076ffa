'use client';

import { useRouter } from 'next/navigation';
import ScrollView from '@/components/globals/ScrollView';
import TopBar from '@/components/manage-customers/TopBar';
import { flex } from '@/styled-system/patterns';
import { useCompleteId } from '@/hooks/general-report';
import { footerWrapper, scrollViewContainer } from './style';
import TankInfo from '@/components/general-report/complete-detail/view-pdf/TankInfo';
import DefectCompletedList from '@/components/general-report/complete-detail/view-pdf/DefectCompletedList';
import DefectUncorrectedList from '@/components/general-report/complete-detail/view-pdf/DefectUncorrectedList';
import CustomerJobSatisfied from '@/components/general-report/complete-detail/view-pdf/CustomerJobSatisfied';
import { ETaskStatus } from '@/utilities/types/enums/Task';
import { useMemo, useState } from 'react';
import { css } from '@/styled-system/css';
import { SendEmailModal } from '@/components/general-report/non-compliance-detail/SendEmailModal';
import { MailIcon } from '@/components/globals/Icons';
import Button from '@/components/globals/Button';
import LabReportsList from '@/components/general-report/complete-detail/view-pdf/LabReportsList';
import DownloadIcon from '@/components/globals/Icons/DownloadIcon';
import NoDefectsList from "@/components/general-report/complete-detail/view-pdf/NoDefects";

type ViewPdfProps = {
    params: { id: string };
    searchParams: { [key: string]: string | string[] | undefined };
};

export default function ViewPdfPage({ params }: ViewPdfProps) {
    const router = useRouter();
    const [open, setOpen] = useState<boolean>(false);
    const { data } = useCompleteId(params?.id);
    const { completedTasks, uncorrectedTasks, preInspection } = useMemo(() => {
        const completedTasks = (data?.tasks || [])?.filter(
            (task) => task?.status === ETaskStatus['COMPLETED']
        );
        const uncorrectedTasks = data?.nonCompliance?.tasks

        const preInspection = data?.preInspection?.tasks;

        return { completedTasks, uncorrectedTasks, preInspection };
    }, [data?.tasks, data?.nonCompliance?.tasks, data?.preInspection?.tasks]);
    const handleSendEmail = async (taskId: string) => {
        setOpen(true);
    };

    const handleClickSendEmail = () => {
        setOpen(!open);
    };
    if (!data) return null;

    return (
        <>
            <div className={flex({ flex: 1, direction: 'column' })}>
                <TopBar
                    onBack={() => {
                        router.back();
                    }}
                    title={''}
                    subTitle={data?.code}
                    primaryButtonVisible={false}
                />
                <ScrollView className={scrollViewContainer}>
                    <TankInfo tank={data?.tank} />
                    {completedTasks.length > 0 && (
                        <DefectCompletedList
                            data={completedTasks}
                            staffs={data?.staffs || []}
                            date={data?.completedDate || ''}
                        />
                    )}
                    {
                        uncorrectedTasks && uncorrectedTasks?.length > 0 && (
                        <DefectUncorrectedList data={uncorrectedTasks} />
                    )}
                    {
                        preInspection && preInspection?.length > 0 &&
                        <NoDefectsList data={preInspection || []}/>
                    }
                    <LabReportsList data={data?.labReports || []} />
                    <CustomerJobSatisfied
                        signatureImg={data?.customerSignature}
                        customerName={
                            data?.tank?.location?.customer?.name || ''
                        }
                    />
                </ScrollView>
                <div className={footerWrapper}>
                    {/*<Button size="medium" type="button">*/}
                    {/*    <DownloadIcon*/}
                    {/*        width={18}*/}
                    {/*        height={18}*/}
                    {/*        className={css({*/}
                    {/*            color: 'white',*/}
                    {/*        })}*/}
                    {/*    />*/}
                    {/*    <span>Download</span>*/}
                    {/*</Button>*/}
                    <Button
                        onClick={handleClickSendEmail}
                        size="medium"
                        type="button"
                        visual="outline_primary"
                    >
                        <MailIcon
                            width={18}
                            height={18}
                            className={css({
                                '&>path': {
                                    stroke: 'primary.100',
                                    strokeWidth: '2px',
                                },
                            })}
                        />
                        <span>Send email</span>
                    </Button>
                </div>
            </div>
            <SendEmailModal
                isOpen={open}
                onClose={handleClickSendEmail}
                tank={data?.tank}
                location={data?.tank?.location}
                staffs={data?.staffs}
                tasks={data?.tasks}
                completedDate={data?.createdAt}
                assignTaskId={data?.id as string}
            />
        </>
    );
}
