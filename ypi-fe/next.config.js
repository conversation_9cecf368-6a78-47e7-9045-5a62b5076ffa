/** @type {import('next').NextConfig} */
const nextConfig = {
    output: 'standalone',
    typescript:{
        ignoreBuildErrors: true,
    },
    eslint:{
        ignoreDuringBuilds: true,
    },
    env: {
        NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
        NEXTAUTH_URL: process.env.APP_HOSTNAME,
        APP_HOSTNAME: process.env.APP_HOSTNAME,
        APP_API_HOST: process.env.APP_API_HOST,
        GOOGLE_MAPS_API_KEY: process.env.GOOGLE_MAPS_API_KEY,
    },
    ignoreBuildErrors: true,
};

module.exports = nextConfig;
